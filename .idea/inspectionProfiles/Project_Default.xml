<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="20">
            <item index="0" class="java.lang.String" itemvalue="scipy" />
            <item index="1" class="java.lang.String" itemvalue="six" />
            <item index="2" class="java.lang.String" itemvalue="opencv-python" />
            <item index="3" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="4" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="5" class="java.lang.String" itemvalue="packaging" />
            <item index="6" class="java.lang.String" itemvalue="cycler" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="fonttools" />
            <item index="9" class="java.lang.String" itemvalue="matplotlib" />
            <item index="10" class="java.lang.String" itemvalue="imutils" />
            <item index="11" class="java.lang.String" itemvalue="pyparsing" />
            <item index="12" class="java.lang.String" itemvalue="Pillow" />
            <item index="13" class="java.lang.String" itemvalue="lazy-object-proxy" />
            <item index="14" class="java.lang.String" itemvalue="colorama" />
            <item index="15" class="java.lang.String" itemvalue="mccabe" />
            <item index="16" class="java.lang.String" itemvalue="pylint" />
            <item index="17" class="java.lang.String" itemvalue="wrapt" />
            <item index="18" class="java.lang.String" itemvalue="isort" />
            <item index="19" class="java.lang.String" itemvalue="astroid" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E501" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>