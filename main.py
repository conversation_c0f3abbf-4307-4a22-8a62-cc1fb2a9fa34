'''
This script demonstrates how to send G-code commands to a Duet 3D printer controller
using the official HTTP API as documented at:
https://github.com/Duet3D/RepRapFirmware/wiki/HTTP-requests
'''

import requests
import time
import atexit

# Global session management
session_active = False

def connect_to_printer(ip, password="reprap"):
    """Establish connection to the Duet printer"""
    global session_active
    url = f"http://{ip}/rr_connect"
    params = {"password": password}

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        result = response.json()

        if result.get("err") == 0:
            session_active = True
            print(f"Connected to printer. Session timeout: {result.get('sessionTimeout', 'unknown')}ms")
            print(f"Board type: {result.get('boardType', 'unknown')}")
            return True
        else:
            print(f"Connection failed with error code: {result.get('err')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to printer: {e}")
        return False

def disconnect_from_printer(ip):
    """Disconnect from the Duet printer"""
    global session_active
    if not session_active:
        return

    url = f"http://{ip}/rr_disconnect"
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        result = response.json()
        if result.get("err") == 0:
            print("Disconnected from printer")
        session_active = False
    except requests.exceptions.RequestException as e:
        print(f"Error disconnecting: {e}")

def send_gcode(ip, command):
    """Send G-code command and return buffer space info"""
    if not session_active:
        print("Error: Not connected to printer")
        return None

    url = f"http://{ip}/rr_gcode"
    params = {"gcode": command}

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        result = response.json()
        buffer_space = result.get("buff", 0)

        print(f"Sent '{command}', buffer space: {buffer_space}")

        # Wait if buffer is getting low (less than 100 bytes)
        if buffer_space < 100:
            print("Buffer space low, waiting...")
            time.sleep(0.5)
        else:
            time.sleep(0.1)  # Small delay after sending command

        return result
    except requests.exceptions.RequestException as e:
        print(f"Error sending '{command}': {e}")
        time.sleep(0.5)  # Longer delay on error
        return None

def get_reply(ip):
    """Get the last G-code reply"""
    if not session_active:
        return None

    url = f"http://{ip}/rr_reply"
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        return response.text.strip()
    except requests.exceptions.RequestException as e:
        print(f"Error getting reply: {e}")
        return None

def wait_for_idle(ip, max_wait_time=30):
    """Wait for printer to become idle using the official rr_model endpoint"""
    if not session_active:
        print("Error: Not connected to printer")
        return False

    start_time = time.time()
    consecutive_idle_count = 0
    last_status = None

    while True:
        try:
            # Check for timeout
            if time.time() - start_time > max_wait_time:
                print(f"Warning: Timeout waiting for idle status after {max_wait_time}s")
                return False

            # Use the official rr_model endpoint for status
            url = f"http://{ip}/rr_model"
            params = {"key": "state.status"}
            response = requests.get(url, params=params, timeout=5)
            response.raise_for_status()
            status_data = response.json()

            # The result field contains the status
            status = status_data.get('result')
            if status == 'idle':
                consecutive_idle_count += 1
                # Wait for 2 consecutive idle readings to ensure stability
                if consecutive_idle_count >= 2:
                    print("Printer is idle.")
                    return True
            else:
                consecutive_idle_count = 0
                if status != last_status:
                    print(f"Printer status: {status}")
                    last_status = status

            time.sleep(0.3)  # Balanced polling interval
        except requests.exceptions.RequestException as e:
            print(f"Error querying status: {e}")
            time.sleep(1)  # Wait longer on communication error
            consecutive_idle_count = 0
            return False

def zigzag_move(ip, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y):
    send_gcode(ip, f"M204 P{acceleration} T{acceleration}")
    send_gcode(ip, f"G1 X{start_x} Y{start_y} F6000")
    wait_for_idle(ip)

    current_y = start_y
    feedrate = f"F{speed}"  # Speed in mm/min

    for row in range(num_rows):
        row_label = chr(65 + row)  # A, B, C, etc.
        if row % 2 == 0:  # Even rows: left to right
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                send_gcode(ip, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(ip)  # Wait until the move is completed
                print(f"Position: {row_label}{col + 1} (X:{target_x:.2f}, Y:{current_y:.2f})")
                time.sleep(0.2)  # Small pause between positions
        else:  # Odd rows: right to left
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                send_gcode(ip, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                wait_for_idle(ip)  # Wait until the move is completed
                print(f"Position: {row_label}{col + 1} (X:{target_x:.2f}, Y:{current_y:.2f})")
                time.sleep(0.2)  # Small pause between positions

        # Move to next row (down by step_y)
        if row < num_rows - 1:  # Don't move Y after the last row
            current_y -= step_y
            send_gcode(ip, f"G1 Y{current_y:.2f} {feedrate}")
            wait_for_idle(ip)

def run_macro(ip, macro_name):
    # M98 command is used to execute macros
    # Provide the path relative to the /sys folder where macros are stored
    send_gcode(ip, f"M98 P\"/macros/{macro_name}\"")


# Parameters
IP_ADDRESS = "*************"
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 16
NUM_ROWS = 24
SPEED = 6000
ACCELERATION = 1000
START_X = 46.6
START_Y = 148.1

# Example usage
send_gcode(IP_ADDRESS, "G28")
wait_for_idle(IP_ADDRESS)
zigzag_move(IP_ADDRESS, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION, START_X, START_Y)

#run_macro(IP_ADDRESS, "zigzag.g")
#wait_for_idle(IP_ADDRESS)
#run_macro(IP_ADDRESS, "eject.g")
