'''
This script demonstrates how to send G-code commands to a Duet 3D printer controller
using the official HTTP API as documented at:
https://github.com/Duet3D/RepRapFirmware/wiki/HTTP-requests
'''

import requests
import time
import atexit

# Global session management
session_active = False

def connect_to_printer(ip, password="reprap"):
    """Establish connection to the Duet printer"""
    global session_active
    url = f"http://{ip}/rr_connect"
    params = {"password": password}

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        result = response.json()

        if result.get("err") == 0:
            session_active = True
            print(f"Connected to printer. Session timeout: {result.get('sessionTimeout', 'unknown')}ms")
            print(f"Board type: {result.get('boardType', 'unknown')}")
            return True
        else:
            print(f"Connection failed with error code: {result.get('err')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to printer: {e}")
        return False

def disconnect_from_printer(ip):
    """Disconnect from the Duet printer"""
    global session_active
    if not session_active:
        return

    url = f"http://{ip}/rr_disconnect"
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        result = response.json()
        if result.get("err") == 0:
            print("Disconnected from printer")
        session_active = False
    except requests.exceptions.RequestException as e:
        print(f"Error disconnecting: {e}")

def send_gcode(ip, command, debug=False):
    """Send G-code command and return buffer space info"""
    if not session_active:
        print("Error: Not connected to printer")
        return None

    start_time = time.time()
    url = f"http://{ip}/rr_gcode"
    params = {"gcode": command}

    try:
        if debug:
            print(f"🔄 Sending: '{command}'")

        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        result = response.json()
        buffer_space = result.get("buff", 0)

        comm_time = (time.time() - start_time) * 1000  # Convert to ms

        if debug:
            print(f"✅ Sent '{command}' in {comm_time:.1f}ms, buffer: {buffer_space} bytes")
        else:
            print(f"Sent '{command}', buffer: {buffer_space} bytes")

        # Wait if buffer is getting low (less than 100 bytes)
        if buffer_space < 100:
            print(f"⚠️  Buffer space low ({buffer_space}), waiting 500ms...")
            time.sleep(0.5)
        else:
            time.sleep(0.1)  # Small delay after sending command

        return result
    except requests.exceptions.RequestException as e:
        comm_time = (time.time() - start_time) * 1000
        print(f"❌ Error sending '{command}' after {comm_time:.1f}ms: {e}")
        time.sleep(0.5)  # Longer delay on error
        return None

def get_reply(ip):
    """Get the last G-code reply"""
    if not session_active:
        return None

    url = f"http://{ip}/rr_reply"
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        return response.text.strip()
    except requests.exceptions.RequestException as e:
        print(f"Error getting reply: {e}")
        return None



def wait_for_move_completion(ip, max_wait_time=60, verbose=False, debug=False):
    """Wait for current move to complete - fast mode by default"""
    if not session_active:
        print("Error: Not connected to printer")
        return False

    start_time = time.time()
    last_status_print = 0
    status_checks = 0
    last_status = None

    if debug:
        print(f"🔍 Starting move completion check...")

    while True:
        try:
            elapsed = time.time() - start_time

            # Check for timeout
            if elapsed > max_wait_time:
                print(f"⏰ Timeout waiting for move completion after {max_wait_time}s")
                return True  # Proceed anyway

            # Get overall status
            status_check_start = time.time()
            status_response = requests.get(f"http://{ip}/rr_model?key=state.status", timeout=5)
            status_check_time = (time.time() - status_check_start) * 1000
            status_checks += 1

            if status_response.status_code == 200:
                status_data = status_response.json()
                status = status_data.get('result', 'unknown')

                # Debug: Show status check timing
                if debug and status != last_status:
                    print(f"📊 Status check #{status_checks}: '{status}' (took {status_check_time:.1f}ms)")
                    last_status = status

                # Print status updates every 5 seconds in verbose mode
                if verbose and (elapsed - last_status_print) > 5:
                    print(f"Printer status: {status} (elapsed: {elapsed:.1f}s, checks: {status_checks})")
                    last_status_print = elapsed

                if status == 'idle':
                    total_time = elapsed * 1000
                    if debug:
                        print(f"✅ Move completed in {total_time:.1f}ms after {status_checks} status checks")
                    elif verbose:
                        print("Move completed - printer idle")
                    return True

            time.sleep(0.1)  # Fast polling by default

        except requests.exceptions.RequestException as e:
            comm_error_time = (time.time() - status_check_start) * 1000 if 'status_check_start' in locals() else 0
            print(f"❌ Error checking move completion after {comm_error_time:.1f}ms: {e}")
            time.sleep(1)
            if time.time() - start_time > max_wait_time:
                return False



def simulate_image_capture(position_name, capture_time=0.2):
    """Simulate image capture - replace this with actual camera code later"""
    print(f"📸 Capturing image at {position_name}...")
    time.sleep(capture_time)  # Simulate camera capture time
    print(f"✅ Image captured at {position_name}")

def zigzag_move_with_capture(ip, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y, capture_time=0.2, pause_time=0.5, debug=False):
    """Zigzag movement optimized for image capture at each position"""
    print("Setting up movement parameters...")
    send_gcode(ip, f"M204 P{acceleration} T{acceleration}", debug=debug)
    send_gcode(ip, f"G1 X{start_x} Y{start_y} F25000", debug=debug)
    wait_for_move_completion(ip, verbose=True, debug=debug)  # Wait for initial positioning

    current_y = start_y
    feedrate = f"F{speed}"
    total_positions = 0
    total_move_time = 0
    total_capture_time = 0
    total_pause_time = 0

    print(f"Starting zigzag pattern: {num_rows} rows x {num_columns} columns")
    print(f"Image capture time: {capture_time}s, Additional pause: {pause_time}s")
    if debug:
        print(f"🐛 Debug mode enabled - detailed timing information will be shown")

    for row in range(num_rows):
        row_label = chr(65 + row)
        print(f"\n--- Processing row {row_label} ---")

        if row % 2 == 0:  # Even rows: left to right
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                position_name = f"{row_label}{col + 1}"

                # 1. Send move command
                move_start = time.time()
                if debug:
                    print(f"🎯 Moving to {position_name} (X:{target_x:.2f}, Y:{current_y:.2f})")
                else:
                    print(f"Moving to {position_name} (X:{target_x:.2f}, Y:{current_y:.2f})")
                send_gcode(ip, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}", debug=debug)

                # 2. Wait for move to complete
                wait_for_move_completion(ip, debug=debug)
                move_time = time.time() - move_start
                total_move_time += move_time

                if debug:
                    print(f"⏱️  Total move time: {move_time*1000:.1f}ms")

                # 3. Take image (this is where your camera code will go)
                capture_start = time.time()
                simulate_image_capture(position_name, capture_time)
                capture_elapsed = time.time() - capture_start
                total_capture_time += capture_elapsed

                # 4. Additional pause if needed
                if pause_time > 0:
                    pause_start = time.time()
                    if debug:
                        print(f"⏸️  Additional pause: {pause_time}s")
                    else:
                        print(f"Additional pause: {pause_time}s")
                    time.sleep(pause_time)
                    pause_elapsed = time.time() - pause_start
                    total_pause_time += pause_elapsed

                total_positions += 1

        else:  # Odd rows: right to left
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                position_name = f"{row_label}{col + 1}"

                # 1. Send move command
                move_start = time.time()
                if debug:
                    print(f"🎯 Moving to {position_name} (X:{target_x:.2f}, Y:{current_y:.2f})")
                else:
                    print(f"Moving to {position_name} (X:{target_x:.2f}, Y:{current_y:.2f})")
                send_gcode(ip, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}", debug=debug)

                # 2. Wait for move to complete
                wait_for_move_completion(ip, debug=debug)
                move_time = time.time() - move_start
                total_move_time += move_time

                if debug:
                    print(f"⏱️  Total move time: {move_time*1000:.1f}ms")

                # 3. Take image (this is where your camera code will go)
                capture_start = time.time()
                simulate_image_capture(position_name, capture_time)
                capture_elapsed = time.time() - capture_start
                total_capture_time += capture_elapsed

                # 4. Additional pause if needed
                if pause_time > 0:
                    pause_start = time.time()
                    if debug:
                        print(f"⏸️  Additional pause: {pause_time}s")
                    else:
                        print(f"Additional pause: {pause_time}s")
                    time.sleep(pause_time)
                    pause_elapsed = time.time() - pause_start
                    total_pause_time += pause_elapsed

                total_positions += 1

        # Move to next row
        if row < num_rows - 1:
            current_y -= step_y
            row_move_start = time.time()
            if debug:
                print(f"🔽 Moving to next row (Y:{current_y:.2f})")
            else:
                print(f"Moving to next row (Y:{current_y:.2f})")
            send_gcode(ip, f"G1 Y{current_y:.2f} {feedrate}", debug=debug)
            wait_for_move_completion(ip, debug=debug)
            row_move_time = time.time() - row_move_start
            total_move_time += row_move_time

            if debug:
                print(f"⏱️  Row change time: {row_move_time*1000:.1f}ms")

    print(f"\n🎉 Zigzag complete! Visited {total_positions} positions total.")
    if debug:
        print(f"📊 Timing Summary:")
        print(f"   Total move time: {total_move_time:.2f}s")
        print(f"   Total capture time: {total_capture_time:.2f}s")
        print(f"   Total pause time: {total_pause_time:.2f}s")
        print(f"   Average per position: {(total_move_time + total_capture_time + total_pause_time)/total_positions:.2f}s")



def zigzag_move(ip, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y, pause_time=0.0):
    """Main zigzag function - optimized for image capture workflow"""
    return zigzag_move_with_capture(ip, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y, capture_time=0.0, pause_time=pause_time)

def run_macro(ip, macro_name):
    # M98 command is used to execute macros
    # Provide the path relative to the /sys folder where macros are stored
    send_gcode(ip, f"M98 P\"/macros/{macro_name}\"")


# Parameters
IP_ADDRESS = "*************"
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 16
NUM_ROWS = 24
SPEED = 25000
ACCELERATION = 5000
START_X = 50
START_Y = 15

# Register cleanup function
def cleanup():
    disconnect_from_printer(IP_ADDRESS)

atexit.register(cleanup)

# Example usage
if __name__ == "__main__":
    print("Connecting to Duet printer...")
    if not connect_to_printer(IP_ADDRESS):
        print("Failed to connect to printer. Exiting.")
        exit(1)

    try:
        print("Homing printer...")
        send_gcode(IP_ADDRESS, "G28")
        print("Waiting for homing to complete...")
        wait_for_move_completion(IP_ADDRESS, max_wait_time=120, verbose=True)  # Longer timeout for homing

        print("Starting zigzag movement...")
        # Enable debug mode to see detailed timing information
        zigzag_move_with_capture(IP_ADDRESS, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION, START_X, START_Y, capture_time=0.0, pause_time=0.5, debug=True)

        print("Movement complete!")

    except KeyboardInterrupt:
        print("\nOperation interrupted by user")
    except Exception as e:
        print(f"Error during operation: {e}")
    finally:
        disconnect_from_printer(IP_ADDRESS)

#run_macro(IP_ADDRESS, "zigzag.g")
#wait_for_idle(IP_ADDRESS)
#run_macro(IP_ADDRESS, "eject.g")
