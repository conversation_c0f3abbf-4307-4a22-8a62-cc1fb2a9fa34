'''
This script demonstrates how to send G-code commands to a Duet 3D printer controller
'''

import requests
import time

def send_gcode(ip, command):
    url = f"http://{ip}/rr_gcode?gcode={command}"
    try:
        response = requests.get(url)
        response.raise_for_status()  # Raises an HTTPError for bad responses
        print(f"Sent '{command}', received: {response.json()}")
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error sending '{command}': {e}")
        return None

def wait_for_idle(ip):
    while True:
        try:
            # Use the newer rr_model endpoint instead of rr_status
            url = f"http://{ip}/rr_model?key=state.status"
            response = requests.get(url)
            response.raise_for_status()
            status_data = response.json()
            
            # The structure of the response is different with rr_model
            if status_data.get('result') == 'I':
                print("Printer is idle.")
                break
            print(f"Printer status: {status_data.get('result')}")
            time.sleep(0.2)  # Polling interval
        except requests.exceptions.RequestException as e:
            print(f"Error querying status: {e}")
            break

def zigzag_move(ip, step_x, step_y, num_columns, num_rows, speed, acceleration):
    send_gcode(ip, f"M204 P{acceleration} T{acceleration}")

    current_x = 0
    start_y = 0  # Assuming you start at y=0
    feedrate = f"F{speed}"  # Speed in mm/min

    for col in range(num_columns):
        for row in range(num_rows):
            target_y = start_y + (row * step_y)
            send_gcode(ip, f"G1 Y{target_y:.2f} {feedrate}")
            wait_for_idle(ip)  # Wait until the move is completed

        current_x += step_x
        send_gcode(ip, f"G1 X{current_x:.2f} Y{start_y:.2f} {feedrate}")
        wait_for_idle(ip)  # Wait until the move is completed

def run_macro(ip, macro_name):
    # M98 command is used to execute macros
    # Provide the path relative to the /sys folder where macros are stored
    send_gcode(ip, f"M98 P\"/macros/{macro_name}\"")


# Parameters
IP_ADDRESS = "*************"
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 16
NUM_ROWS = 24
SPEED = 6000
ACCELERATION = 1000

# Example usage
send_gcode(IP_ADDRESS, "G28")
wait_for_idle(IP_ADDRESS)
#zigzag_move(IP_ADDRESS, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION)

#run_macro(IP_ADDRESS, "zigzag.g")
#wait_for_idle(IP_ADDRESS)
#run_macro(IP_ADDRESS, "eject.g")
