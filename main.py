'''
This script demonstrates how to send G-code commands to a Duet 3D printer controller
using the official HTTP API as documented at:
https://github.com/Duet3D/RepRapFirmware/wiki/HTTP-requests
'''

import requests
import time
import atexit

# Global session management
session_active = False

def connect_to_printer(ip, password="reprap"):
    """Establish connection to the Duet printer"""
    global session_active
    url = f"http://{ip}/rr_connect"
    params = {"password": password}

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        result = response.json()

        if result.get("err") == 0:
            session_active = True
            print(f"Connected to printer. Session timeout: {result.get('sessionTimeout', 'unknown')}ms")
            print(f"Board type: {result.get('boardType', 'unknown')}")
            return True
        else:
            print(f"Connection failed with error code: {result.get('err')}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Error connecting to printer: {e}")
        return False

def disconnect_from_printer(ip):
    """Disconnect from the Duet printer"""
    global session_active
    if not session_active:
        return

    url = f"http://{ip}/rr_disconnect"
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        result = response.json()
        if result.get("err") == 0:
            print("Disconnected from printer")
        session_active = False
    except requests.exceptions.RequestException as e:
        print(f"Error disconnecting: {e}")

def send_gcode(ip, command):
    """Send G-code command and return buffer space info"""
    if not session_active:
        print("Error: Not connected to printer")
        return None

    url = f"http://{ip}/rr_gcode"
    params = {"gcode": command}

    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        result = response.json()
        buffer_space = result.get("buff", 0)

        print(f"Sent '{command}', buffer space: {buffer_space}")

        # Wait if buffer is getting low (less than 100 bytes)
        if buffer_space < 100:
            print("Buffer space low, waiting...")
            time.sleep(0.5)
        else:
            time.sleep(0.1)  # Small delay after sending command

        return result
    except requests.exceptions.RequestException as e:
        print(f"Error sending '{command}': {e}")
        time.sleep(0.5)  # Longer delay on error
        return None

def get_reply(ip):
    """Get the last G-code reply"""
    if not session_active:
        return None

    url = f"http://{ip}/rr_reply"
    try:
        response = requests.get(url, timeout=5)
        response.raise_for_status()
        return response.text.strip()
    except requests.exceptions.RequestException as e:
        print(f"Error getting reply: {e}")
        return None

def get_movement_state(ip):
    """Get detailed movement state information"""
    if not session_active:
        return None

    try:
        # Get movement state information
        url = f"http://{ip}/rr_model"
        params = {"key": "move"}
        response = requests.get(url, params=params, timeout=5)
        response.raise_for_status()
        move_data = response.json()
        return move_data.get('result', {})
    except requests.exceptions.RequestException as e:
        print(f"Error getting movement state: {e}")
        return None

def wait_for_move_completion(ip, max_wait_time=60, verbose=False):
    """Wait for current move to complete - simplified version"""
    if not session_active:
        print("Error: Not connected to printer")
        return False

    start_time = time.time()
    last_status_print = 0
    consecutive_idle_count = 0

    while True:
        try:
            elapsed = time.time() - start_time

            # Check for timeout
            if elapsed > max_wait_time:
                print(f"Warning: Timeout waiting for move completion after {max_wait_time}s")
                return True  # Proceed anyway

            # Get overall status
            status_response = requests.get(f"http://{ip}/rr_model?key=state.status", timeout=5)
            if status_response.status_code == 200:
                status_data = status_response.json()
                status = status_data.get('result', 'unknown')

                # Print status updates every 5 seconds in verbose mode, or when status changes
                if verbose and (elapsed - last_status_print) > 5:
                    print(f"Printer status: {status} (elapsed: {elapsed:.1f}s)")
                    last_status_print = elapsed

                if status == 'idle':
                    consecutive_idle_count += 1
                    # Wait for 3 consecutive idle readings to ensure stability
                    if consecutive_idle_count >= 3:
                        if verbose:
                            print("Move completed - printer confirmed idle")
                        return True
                else:
                    consecutive_idle_count = 0

            time.sleep(0.5)  # Reasonable polling interval

        except requests.exceptions.RequestException as e:
            print(f"Error checking move completion: {e}")
            time.sleep(1)
            # If we can't communicate and have timed out, give up
            if time.time() - start_time > max_wait_time:
                return False

def send_gcode_buffered(ip, command, min_buffer_space=200):
    """Send G-code with intelligent buffering"""
    if not session_active:
        print("Error: Not connected to printer")
        return None

    # Check buffer space before sending
    while True:
        try:
            # Send the command
            url = f"http://{ip}/rr_gcode"
            params = {"gcode": command}
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            result = response.json()
            buffer_space = result.get("buff", 0)

            print(f"Sent '{command}', buffer: {buffer_space} bytes")

            # If buffer space is good, we're done
            if buffer_space >= min_buffer_space:
                return result
            else:
                print(f"Buffer space low ({buffer_space}), waiting for space...")
                time.sleep(0.2)
                continue

        except requests.exceptions.RequestException as e:
            print(f"Error sending '{command}': {e}")
            time.sleep(0.5)
            return None

def wait_for_idle(ip, max_wait_time=30):
    """Legacy function - use wait_for_move_completion instead"""
    return wait_for_move_completion(ip, max_wait_time)

def zigzag_move_buffered(ip, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y):
    """Zigzag movement with intelligent buffering - sends multiple moves without waiting"""
    print("Setting up movement parameters...")
    send_gcode_buffered(ip, f"M204 P{acceleration} T{acceleration}")
    send_gcode_buffered(ip, f"G1 X{start_x} Y{start_y} F6000")
    wait_for_move_completion(ip)  # Wait for initial positioning

    current_y = start_y
    feedrate = f"F{speed}"
    moves_sent = 0

    print(f"Starting zigzag pattern: {num_rows} rows x {num_columns} columns")

    for row in range(num_rows):
        row_label = chr(65 + row)
        print(f"Processing row {row_label}...")

        if row % 2 == 0:  # Even rows: left to right
            for col in range(num_columns):
                target_x = start_x + (col * step_x)
                send_gcode_buffered(ip, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                moves_sent += 1

                # Every few moves, check if we should wait
                if moves_sent % 5 == 0:
                    move_state = get_movement_state(ip)
                    if move_state and move_state.get('queue', 0) > 10:
                        print("Queue getting full, waiting...")
                        wait_for_move_completion(ip)

        else:  # Odd rows: right to left
            for col in reversed(range(num_columns)):
                target_x = start_x + (col * step_x)
                send_gcode_buffered(ip, f"G1 X{target_x:.2f} Y{current_y:.2f} {feedrate}")
                moves_sent += 1

                # Every few moves, check if we should wait
                if moves_sent % 5 == 0:
                    move_state = get_movement_state(ip)
                    if move_state and move_state.get('queue', 0) > 10:
                        print("Queue getting full, waiting...")
                        wait_for_move_completion(ip)

        # Move to next row
        if row < num_rows - 1:
            current_y -= step_y
            send_gcode_buffered(ip, f"G1 Y{current_y:.2f} {feedrate}")
            moves_sent += 1

    # Wait for all moves to complete
    print("Waiting for all moves to complete...")
    wait_for_move_completion(ip)
    print(f"Zigzag complete! Sent {moves_sent} moves total.")

def zigzag_move(ip, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y):
    """Legacy zigzag function - use zigzag_move_buffered for better performance"""
    return zigzag_move_buffered(ip, step_x, step_y, num_columns, num_rows, speed, acceleration, start_x, start_y)

def run_macro(ip, macro_name):
    # M98 command is used to execute macros
    # Provide the path relative to the /sys folder where macros are stored
    send_gcode(ip, f"M98 P\"/macros/{macro_name}\"")


# Parameters
IP_ADDRESS = "*************"
STEP_X = 4.5
STEP_Y = 4.5
NUM_COLUMNS = 16
NUM_ROWS = 24
SPEED = 6000
ACCELERATION = 1000
START_X = 46.6
START_Y = 148.1

# Register cleanup function
def cleanup():
    disconnect_from_printer(IP_ADDRESS)

atexit.register(cleanup)

# Example usage
if __name__ == "__main__":
    print("Connecting to Duet printer...")
    if not connect_to_printer(IP_ADDRESS):
        print("Failed to connect to printer. Exiting.")
        exit(1)

    try:
        print("Homing printer...")
        send_gcode(IP_ADDRESS, "G28")
        print("Waiting for homing to complete...")
        wait_for_move_completion(IP_ADDRESS, max_wait_time=120, verbose=True)  # Longer timeout for homing

        print("Starting zigzag movement...")
        zigzag_move(IP_ADDRESS, STEP_X, STEP_Y, NUM_COLUMNS, NUM_ROWS, SPEED, ACCELERATION, START_X, START_Y)

        print("Movement complete!")

    except KeyboardInterrupt:
        print("\nOperation interrupted by user")
    except Exception as e:
        print(f"Error during operation: {e}")
    finally:
        disconnect_from_printer(IP_ADDRESS)

#run_macro(IP_ADDRESS, "zigzag.g")
#wait_for_idle(IP_ADDRESS)
#run_macro(IP_ADDRESS, "eject.g")
